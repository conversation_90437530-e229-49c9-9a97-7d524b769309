"""
FIJI/ImageJ Compact Batch Particle Classification Script v2.8
Automated dual Weka classification (porosity + shape) with color coding, numbering, legend overlay, and statistics export.
Includes formatted Excel output with thick borders for better data visualization.

Version 2.8 Changes:
- Refactored code for improved readability and maintainability
- Reduced unnecessary Unicode normalization overhead
- Consolidated related functions for better code organization
- Simplified processing logic while preserving all functionality
- Added SHOW_IMAGES_AFTER_PROCESSING option to control image display independent of saving
- Removed HIDE_IMAGES_DURING_PROCESSING option (incompatible with macro requirements)

NECESSARY ADAPTATIONS:
- Calibration: Number of pixels that is equivalent to a known number of microns
- MACRO_PATH: Path to ImageJ macro file extracting the needed parameters of the examined particles
- POROSITY_MODEL_PATH: Path to porosity classification model
- SHAPE_MODEL_PATH: Path to shape classification model
- USER_WEKA_JAR_PATH: Path to Weka JAR (if not in ImageJ classpath)
- IMPORT_DIR: Directory containing images (or use interactive selection)
- PROCESSED_IMAGES_OUTPUT_DIR: Directory to save processed images (if SAVE_PROCESSED_IMAGES is True)
- FILE_TYPES: File extensions to process
"""

# === IMPORTS ===
from ij import IJ, WindowManager
from ij.measure import ResultsTable
from ij.plugin.frame import RoiManager
from ij.gui import Roi, TextRoi, Overlay
from java.io import FileInputStream, File, FileOutputStream
from java.awt import Color, Font
from java.lang import ClassLoader, Class
from java.util import ArrayList
from javax.swing import JFileChooser
import os, time

# === CONFIGURATION ===

# Calibration
pixels = 214
microns = 50

# Paths and settings
MACRO_PATH = "C:/Users/<USER>/Fiji.app/macros/extract_features_for_dual_classification_with_args.ijm"
PORE_MACRO_PATH = "C:/Users/<USER>/Fiji.app/scripts/extract_pore_sizes.ijm"
POROSITY_MODEL_PATH = "C:/Studium/Johann/Partikeluntersuchungen/63um/Modelle/bin_porosity_14_07_b.model"
SHAPE_MODEL_PATH = "C:/Studium/Johann/Partikeluntersuchungen/63um/Modelle/bin_shape_14_07_SCF.model"
USER_WEKA_JAR_PATH ="C:/Program Files/Weka-3-8-6/weka.jar"
IMPORT_DIR = "C:/Studium/Johann/Partikeluntersuchungen/63um/Test"
PROCESSED_IMAGES_OUTPUT_DIR = None
FILE_TYPES = "tif"

# Processing settings
SHOW_IMAGES_AFTER_PROCESSING = False  # Display final colored/processed images after processing
CREATE_LEGEND = False
SAVE_PROCESSED_IMAGES = False
SHOW_PARTICLE_NUMBERS = False

# Features required for porosity classification
default_porosity_features = "total_black_pixels"
POROSITY_REQUIRED_FEATURES = [feat.strip() for feat in default_porosity_features.split(';')]

# Features required for shape classification
default_shape_features = "Solidity;Convexity;FeretRatio"
SHAPE_REQUIRED_FEATURES = [feat.strip() for feat in default_shape_features.split(';')]

# Classification labels and colors
POROSITY_LABELS = ["NonPorous", "Porous"]
SHAPE_LABELS = ["Round", "Imperfect"]
DUAL_COLORS = {
    ("NonPorous", "Round"): Color(0, 255, 0, 100),      # Green
    ("NonPorous", "Imperfect"): Color(100, 0, 100, 100), # Purple
    ("Porous", "Round"): Color(255, 255, 0, 100),       # Yellow
    ("Porous", "Imperfect"): Color(255, 0, 0, 100)      # Red
}

# Global Weka classes
WekaAttribute = WekaInstances = WekaDenseInstance = WekaSerializationHelper = None

# Global pore data storage
all_pore_data = []  # List of dictionaries: [{"image": str, "particle_id": int, "pore_area": float}, ...]

# === UTILITY FUNCTIONS ===
def normalize_filename(filename):
    """Normalize filename for Unicode compatibility."""
    if not filename:
        return ""
    try:
        return filename.replace(u'\u00b5', 'u').replace('µ', 'u').strip()
    except:
        return filename


def is_image_in_current_names(image_name_rich, current_image_names):
    """
    Compare RichString image_name with set of RichString current_image_names
    using string content comparison with Unicode safety for Jython.
    """
    try:
        # Convert the Excel image_name to string safely
        if hasattr(image_name_rich, 'getString'):
            image_str = image_name_rich.getString()
        else:
            image_str = str(image_name_rich)
        
        # Compare with each item in current_image_names
        for current_name_rich in current_image_names:
            try:
                # Convert current name to string safely
                if hasattr(current_name_rich, 'getString'):
                    current_str = current_name_rich.getString()
                else:
                    current_str = str(current_name_rich)
                
                # Direct string comparison
                if image_str == current_str:
                    return True
                    
                # Also try normalized comparison as fallback
                if normalize_filename(image_str) == normalize_filename(current_str):
                    return True
                    
            except Exception as e:
                # If string conversion fails, skip this comparison
                IJ.log("Warning: Could not compare with current name: {}".format(str(e)))
                continue
                
        return False
        
    except Exception as e:
        IJ.log("Warning: Could not process image name for comparison: {}".format(str(e)))
        return False


def load_existing_pore_data_from_excel(excel_path, current_image_names=None):
    """Load existing pore data from Excel file, filtered by images currently in the input directory."""
    global all_pore_data

    if current_image_names is None:
        current_image_names = set()
    else:
        IJ.log("Loading pore data for {} current images".format(len(current_image_names)))

    try:
        from org.apache.poi.xssf.usermodel import XSSFWorkbook
        from java.io import FileInputStream

        # Open the Excel file
        file_stream = FileInputStream(excel_path)
        workbook = XSSFWorkbook(file_stream)

        # Look for the "Pore Analysis" sheet
        pore_sheet = None
        for i in range(workbook.getNumberOfSheets()):
            sheet = workbook.getSheetAt(i)
            if sheet.getSheetName() == "Pore Analysis":
                pore_sheet = sheet
                break

        if pore_sheet is None:
            file_stream.close()
            workbook.close()
            IJ.log("No 'Pore Analysis' worksheet found in existing Excel file")
            return

        # Read pore data (skip header row) and filter by current images
        total_rows = pore_sheet.getLastRowNum()
        existing_pore_count = 0
        filtered_pore_count = 0

        IJ.log("Reading existing pore data from Excel file ({} rows)".format(total_rows))

        for row_idx in range(1, total_rows + 1):
            row = pore_sheet.getRow(row_idx)
            if row is None:
                continue

            # Check if all required cells exist first
            image_cell = row.getCell(1)
            particle_id_cell = row.getCell(2)
            pore_area_cell = row.getCell(3)

            if not (image_cell and particle_id_cell and pore_area_cell):
                continue

            # Read image name with Unicode safety
            image_name = None
            try:
                raw_image_name = image_cell.getRichStringCellValue()
                if raw_image_name and raw_image_name != "":
                    image_name = raw_image_name
            except:
                try:
                    raw_image_name = image_cell.getStringCellValue()
                    if raw_image_name:
                        image_name = raw_image_name
                except:
                    continue

            if not image_name:
                continue

            # Read numeric values
            try:
                particle_id = int(particle_id_cell.getNumericCellValue())
                pore_area = float(pore_area_cell.getNumericCellValue())
            except:
                continue

            existing_pore_count += 1

            # Only include pore data for images currently in the input directory
            if is_image_in_current_names(image_name, current_image_names):
                all_pore_data.append({
                    "image": image_name,
                    "particle_id": particle_id,
                    "pore_area": pore_area
                })
                filtered_pore_count += 1

        file_stream.close()
        workbook.close()

        if existing_pore_count > 0:
            IJ.log("Found {} existing pore measurements in Excel file".format(existing_pore_count))
            IJ.log("Preserved {} pore measurements for images currently in directory".format(filtered_pore_count))

    except Exception as e:
        IJ.log("Warning: Could not load existing pore data from Excel: {}".format(str(e)))


def setup_weka_and_validate():
    """Setup Weka classes and validate all required paths."""
    global WekaAttribute, WekaInstances, WekaDenseInstance, WekaSerializationHelper
    
    # Setup Weka
    try:
        Class.forName("weka.core.Attribute")
    except:
        if USER_WEKA_JAR_PATH and os.path.exists(USER_WEKA_JAR_PATH):
            try:
                from java.net import URL
                jar_url = File(USER_WEKA_JAR_PATH).toURI().toURL()
                ClassLoader.getSystemClassLoader().getClass().getDeclaredMethod("addURL", [URL]).invoke(ClassLoader.getSystemClassLoader(), [jar_url])
            except Exception as e:
                raise Exception("Failed to load Weka: " + str(e))
    
    try:
        from weka.core import Attribute as WekaAttribute, Instances as WekaInstances, DenseInstance as WekaDenseInstance, SerializationHelper as WekaSerializationHelper
    except Exception as e:
        raise Exception("Failed to import Weka classes: " + str(e))
    
    # Validate directory
    global IMPORT_DIR
    if not IMPORT_DIR or not os.path.exists(IMPORT_DIR):
        try:
            chooser = JFileChooser()
            chooser.setDialogTitle("Select Directory Containing Images to Analyze")
            chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY)
            if chooser.showOpenDialog(None) == JFileChooser.APPROVE_OPTION:
                IMPORT_DIR = chooser.getSelectedFile().getAbsolutePath()
            else:
                raise Exception("No directory selected")
        except:
            raise Exception("No valid input directory")
    
    # Validate required files
    for path, name in [(MACRO_PATH, "Macro"), (PORE_MACRO_PATH, "Pore macro"), (POROSITY_MODEL_PATH, "Porosity model"), (SHAPE_MODEL_PATH, "Shape model")]:
        if not os.path.exists(path):
            raise Exception("{} file not found: {}".format(name, path))


def load_weka_model(model_path):
    """Load and return Weka model."""
    try:
        file_stream = FileInputStream(model_path)
        model = WekaSerializationHelper.read(file_stream)
        file_stream.close()
        return model if hasattr(model, 'classifyInstance') else None
    except Exception as e:
        IJ.log("ERROR loading model {}: {}".format(model_path, str(e)))
        return None

def load_existing_csv_data(csv_path, current_filenames):
    """Load existing CSV data and return processed image names, existing data, and existing statistics."""
    existing_data = []
    processed_images = set()
    existing_stats = {
        'dual': {(p, s): 0 for p in POROSITY_LABELS for s in SHAPE_LABELS},
        'porosity': {label: 0 for label in POROSITY_LABELS},
        'shape': {label: 0 for label in SHAPE_LABELS}
    }

    if not os.path.exists(csv_path):
        return existing_data, processed_images, existing_stats

    try:
        with open(csv_path, 'rb') as f:
            raw_data = f.read()

        try:
            decoded_data = raw_data.decode('latin-1')
        except UnicodeDecodeError:
            decoded_data = raw_data.decode('utf-8')

        lines = decoded_data.splitlines(True)

        if len(lines) > 1:  # Has header + data
            existing_data.append(lines[0].strip())  # Keep header
            dual_start_idx = 1  # After 'Image' column
            dual_end_idx = dual_start_idx + len(POROSITY_LABELS) * len(SHAPE_LABELS)
            porosity_start_idx = dual_end_idx
            porosity_end_idx = porosity_start_idx + len(POROSITY_LABELS)
            shape_start_idx = porosity_end_idx

            seen_images = set()
            for line in lines[1:]:
                line = line.strip()
                # Skip TOTAL rows and calibration comment lines
                if line and not line.startswith('TOTAL') and not line.startswith('#'):
                    parts = line.split(',')
                    if len(parts) > 0:
                        image_name = normalize_filename(parts[0])
                        if image_name not in seen_images and image_name in current_filenames:
                            processed_images.add(image_name)
                            existing_data.append(line)
                            seen_images.add(image_name)

                            # Parse statistics from this row
                            try:
                                dual_idx = dual_start_idx
                                for p in POROSITY_LABELS:
                                    for s in SHAPE_LABELS:
                                        if dual_idx < len(parts):
                                            existing_stats['dual'][(p, s)] += int(parts[dual_idx])
                                        dual_idx += 1

                                porosity_idx = porosity_start_idx
                                for label in POROSITY_LABELS:
                                    if porosity_idx < len(parts):
                                        existing_stats['porosity'][label] += int(parts[porosity_idx])
                                    porosity_idx += 1

                                shape_idx = shape_start_idx
                                for label in SHAPE_LABELS:
                                    if shape_idx < len(parts):
                                        existing_stats['shape'][label] += int(parts[shape_idx])
                                    shape_idx += 1
                            except (ValueError, IndexError):
                                pass

        total_existing_particles = sum(existing_stats['porosity'].values())
        if total_existing_particles > 0:
            IJ.log("Loaded existing CSV: {} images already processed ({} particles)".format(
                len(processed_images), total_existing_particles))

        return existing_data, processed_images, existing_stats

    except Exception as e:
        IJ.log("Warning: Could not read existing CSV: " + str(e))
        return [], set(), existing_stats


def is_image_already_processed(image_path, processed_images_from_csv=None):
    """Check if image has already been processed (CSV entry)."""
    image_name = normalize_filename(os.path.basename(image_path))

    # Check CSV first if provided
    if processed_images_from_csv is not None:
        if image_name in processed_images_from_csv:
            return True

    return False


def has_existing_pore_data(image_name):
    """Check if pore data already exists for this image."""
    global all_pore_data
    normalized_name = normalize_filename(image_name)
    return any(pore["image"] == normalized_name for pore in all_pore_data)

def create_formatted_excel_file(csv_path, csv_content_lines):
    """Create a formatted Excel file with thick borders from CSV data and pore analysis."""
    global all_pore_data

    try:
        # Import Apache POI classes
        from org.apache.poi.ss.usermodel import BorderStyle
        from org.apache.poi.xssf.usermodel import XSSFWorkbook

        # Create workbook and sheets
        workbook = XSSFWorkbook()
        sheet = workbook.createSheet("Particle Analysis Results")

        # Create cell styles for borders
        # Style for thick bottom border (header row)
        thick_bottom_style = workbook.createCellStyle()
        thick_bottom_style.setBorderBottom(BorderStyle.THICK)

        # Style for thick right border (column separators)
        thick_right_style = workbook.createCellStyle()
        thick_right_style.setBorderRight(BorderStyle.THICK)

        # Style for thick top border (above total row)
        thick_top_style = workbook.createCellStyle()
        thick_top_style.setBorderTop(BorderStyle.THICK)

        # Combined styles
        header_right_style = workbook.createCellStyle()
        header_right_style.setBorderBottom(BorderStyle.THICK)
        header_right_style.setBorderRight(BorderStyle.THICK)

        total_right_style = workbook.createCellStyle()
        total_right_style.setBorderTop(BorderStyle.THICK)
        total_right_style.setBorderRight(BorderStyle.THICK)

        # Parse CSV content and populate Excel
        total_row_index = -1
        for row_idx, line in enumerate(csv_content_lines):
            line = line.strip()
            if not line:
                continue

            if line.startswith('TOTAL'):
                total_row_index = row_idx

            parts = line.split(',')
            row = sheet.createRow(row_idx)

            for col_idx, value in enumerate(parts):
                cell = row.createCell(col_idx)

                # Try to convert to number if possible
                try:
                    if value.isdigit():
                        cell.setCellValue(int(value))
                    elif value.replace('.', '').replace('-', '').isdigit() and value.count('.') <= 1:
                        cell.setCellValue(float(value))
                    else:
                        cell.setCellValue(value)
                except:
                    cell.setCellValue(value)

                # Apply formatting based on position
                is_header = (row_idx == 0)
                is_total = (row_idx == total_row_index)
                is_separator_col = (col_idx == 0 or col_idx == 4 or col_idx == 6)

                if is_header and is_separator_col:
                    cell.setCellStyle(header_right_style)
                elif is_header:
                    cell.setCellStyle(thick_bottom_style)
                elif is_total and is_separator_col:
                    cell.setCellStyle(total_right_style)
                elif is_total:
                    cell.setCellStyle(thick_top_style)
                elif is_separator_col:
                    cell.setCellStyle(thick_right_style)

        # Auto-size columns
        for col_idx in range(len(csv_content_lines[0].split(','))):
            sheet.autoSizeColumn(col_idx)

        # Create pore data worksheet if we have pore data
        if all_pore_data:
            pore_sheet = workbook.createSheet("Pore Analysis")

            # Create header row
            pore_header_row = pore_sheet.createRow(0)
            headers = ["Pore_ID", "Image", "Particle_ID", "Pore_Area_um2"]
            for col_idx, header in enumerate(headers):
                cell = pore_header_row.createCell(col_idx)
                cell.setCellValue(header)
                cell.setCellStyle(thick_bottom_style)

            # Add pore data rows
            for row_idx, pore_data in enumerate(all_pore_data):
                data_row = pore_sheet.createRow(row_idx + 1)

                # Pore ID
                cell = data_row.createCell(0)
                cell.setCellValue(row_idx + 1)

                # Image name (safe Unicode handling)
                cell = data_row.createCell(1)
                try:
                    safe_image_name = pore_data["image"] if pore_data["image"] else ""
                    cell.setCellValue(safe_image_name)
                except:
                    cell.setCellValue("ERROR")

                # Particle ID
                cell = data_row.createCell(2)
                cell.setCellValue(pore_data["particle_id"])

                # Pore area
                cell = data_row.createCell(3)
                cell.setCellValue(pore_data["pore_area"])

            # Auto-size pore sheet columns
            for col_idx in range(4):
                pore_sheet.autoSizeColumn(col_idx)

            # Create histogram data and plot
            pore_areas = [pore["pore_area"] for pore in all_pore_data]
            if pore_areas:
                min_area = min(pore_areas)
                max_area = max(pore_areas)
                optimal_bins = calculate_optimal_bins(pore_areas, min_area, max_area, len(pore_areas))

                # Create histogram worksheet
                hist_sheet = workbook.createSheet("Pore Size Distribution")

                # Calculate bin edges and frequencies
                bin_width = (max_area - min_area) / optimal_bins if optimal_bins > 0 else 1
                bin_edges = [min_area + i * bin_width for i in range(optimal_bins + 1)]
                bin_frequencies = [0] * optimal_bins

                # Count frequencies
                for area in pore_areas:
                    bin_index = min(int((area - min_area) / bin_width), optimal_bins - 1)
                    bin_frequencies[bin_index] += 1

                # Create histogram data table
                hist_header_row = hist_sheet.createRow(0)
                hist_header_row.createCell(0).setCellValue("Bin_Start")
                hist_header_row.createCell(1).setCellValue("Bin_End")
                hist_header_row.createCell(2).setCellValue("Frequency")

                for i in range(optimal_bins):
                    data_row = hist_sheet.createRow(i + 1)
                    data_row.createCell(0).setCellValue(round(bin_edges[i], 3))
                    data_row.createCell(1).setCellValue(round(bin_edges[i + 1], 3))
                    data_row.createCell(2).setCellValue(bin_frequencies[i])

                # Add comprehensive pore statistics
                sorted_pore_areas = sorted(pore_areas)
                total_pores = len(pore_areas)

                # Calculate quartiles
                q1_index = max(0, min(int(total_pores * 0.25), total_pores - 1))
                q2_index = max(0, min(int(total_pores * 0.50), total_pores - 1))
                q3_index = max(0, min(int(total_pores * 0.75), total_pores - 1))

                min_pore = min(pore_areas)
                max_pore = max(pore_areas)
                mean_pore = sum(pore_areas) / total_pores
                q1_pore = sorted_pore_areas[q1_index]
                q2_pore = sorted_pore_areas[q2_index]
                q3_pore = sorted_pore_areas[q3_index]

                # Add statistics table starting from column E (index 4)
                stats_col = 4
                stats_data = [
                    ("Statistic", "Value (um^2)"),
                    ("Total Pores", total_pores),
                    ("Minimum", round(min_pore, 3)),
                    ("First Quartile (Q1)", round(q1_pore, 3)),
                    ("Median (Q2)", round(q2_pore, 3)),
                    ("Mean", round(mean_pore, 3)),
                    ("Third Quartile (Q3)", round(q3_pore, 3)),
                    ("Maximum", round(max_pore, 3))
                ]

                for row_idx, (stat_name, stat_value) in enumerate(stats_data):
                    if row_idx <= optimal_bins:
                        stats_row = hist_sheet.getRow(row_idx) or hist_sheet.createRow(row_idx)
                    else:
                        stats_row = hist_sheet.createRow(row_idx)

                    stats_row.createCell(stats_col).setCellValue(str(stat_name))
                    stats_row.createCell(stats_col + 1).setCellValue(str(stat_value))

                # Auto-size all columns
                for col_idx in range(6):
                    hist_sheet.autoSizeColumn(col_idx)

                # Generate and embed histogram plot using ImageJ
                try:
                    histogram_image_path = create_imagej_histogram_plot(pore_areas, optimal_bins, min_area, max_area, bin_frequencies, bin_edges)
                    if histogram_image_path:
                        embed_image_in_excel(workbook, hist_sheet, histogram_image_path, 7, 1)
                        IJ.log("Added ImageJ histogram plot to Excel worksheet")
                except Exception as e:
                    IJ.log("Warning: Could not create ImageJ histogram plot: {}".format(str(e)))

                IJ.log("Added pore size histogram with {} bins".format(optimal_bins))

            IJ.log("Added pore analysis worksheet with {} pores".format(len(all_pore_data)))

        # Save Excel file
        excel_path = csv_path.replace('.csv', '.xlsx')
        file_out = FileOutputStream(excel_path)
        workbook.write(file_out)
        file_out.close()
        workbook.close()

        IJ.log("Excel file created with formatting: {}".format(excel_path))
        return True

    except Exception as e:
        IJ.log("Error creating formatted Excel file: {}".format(str(e)))
        return False

# === CORE PROCESSING FUNCTIONS ===
def run_macro_and_extract_features():
    """Run macro and extract particle features."""
    # Clear previous results
    rt = ResultsTable.getResultsTable()
    if rt: rt.reset()
    roim = RoiManager.getInstance()
    if roim: roim.reset()

    # Run macro
    try:
        args = str(pixels)+","+str(microns)
        IJ.runMacroFile(MACRO_PATH, args)
    except Exception as e:
        IJ.log("ERROR: Macro execution failed: " + str(e))
        return None, [], 0

    # Extract features
    rt = ResultsTable.getResultsTable()
    if not rt or rt.getCounter() == 0:
        return None, [], 0

    headings = rt.getHeadings()
    excluded_columns = {"label", "id", "x", "y", "bx", "by", "width", "height"}
    feature_headings = [h for h in headings if h.lower() not in excluded_columns]

    features_list = []
    for row_idx in range(rt.getCounter()):
        row_features = []
        for heading in feature_headings:
            try:
                value = float(rt.getValue(heading, row_idx))
                if str(value).lower() not in ['nan', 'inf', '-inf']:
                    row_features.append(value)
                else:
                    break
            except:
                break
        if len(row_features) == len(feature_headings):
            features_list.append(row_features)

    return features_list, feature_headings, len(features_list)


def extract_pore_sizes(image_name, current_image):
    """Extract pore sizes from current image and store in global array."""
    global all_pore_data

    # Verify we have an active image
    if not current_image:
        IJ.log("Warning: No image provided for pore extraction in {}".format(normalize_filename(image_name)))
        return 0

    # Ensure the image is the current/active image
    try:
        current_image.show()
        WindowManager.setCurrentWindow(current_image.getWindow())
    except:
        # If showing fails, try setting as temp current image as fallback
        try:
            WindowManager.setTempCurrentImage(current_image)
        except Exception as e:
            IJ.log("Warning: Could not set current image for pore extraction in {}: {}".format(image_name, str(e)))
            return 0

    # Clear previous results
    rt = ResultsTable.getResultsTable()
    if rt: rt.reset()
    roim = RoiManager.getInstance()
    if roim: roim.reset()

    # Run pore extraction macro
    try:
        args = str(pixels)+","+str(microns)
        IJ.runMacroFile(PORE_MACRO_PATH, args)
    except Exception as e:
        IJ.log("Warning: Pore extraction failed for {}: {}".format(normalize_filename(image_name), str(e)))
        return 0

    # Extract pore data from results table
    rt = ResultsTable.getResultsTable()
    if not rt or rt.getCounter() == 0:
        return 0

    pore_count = 0
    for row_idx in range(rt.getCounter()):
        try:
            particle_id = int(rt.getValue("Particle_ID", row_idx))
            pore_area = float(rt.getValue("Pore_Area_um2", row_idx))

            # Store pore data
            all_pore_data.append({
                "image": normalize_filename(image_name),
                "particle_id": particle_id,
                "pore_area": pore_area
            })
            pore_count += 1
        except Exception as e:
            IJ.log("Warning: Error reading pore data row {}: {}".format(row_idx, str(e)))
            continue

    return pore_count


def calculate_optimal_bins(data_array, min_val, max_val, sample_size):
    """Calculate optimal number of bins for histogram using multiple methods."""
    import math

    if sample_size <= 0 or len(data_array) == 0:
        return 5

    # Method 1: Sturges' Rule - good for normal distributions
    sturges_bins = int(1 + math.log(sample_size, 2) + 0.5)

    # Method 2: Scott's Rule - based on data variability
    if sample_size > 1:
        mean_val = sum(data_array) / len(data_array)
        variance = sum((x - mean_val) ** 2 for x in data_array) / (len(data_array) - 1)
        std_dev = math.sqrt(variance) if variance > 0 else 0

        if std_dev > 0:
            bin_width = 3.5 * std_dev / (sample_size ** (1.0/3.0))
            scott_bins = int((max_val - min_val) / bin_width + 0.5) if bin_width > 0 else sturges_bins
        else:
            scott_bins = sturges_bins
    else:
        scott_bins = sturges_bins

    # Method 3: Freedman-Diaconis Rule - robust to outliers
    sorted_data = sorted(data_array)
    q1_index = int(sample_size * 0.25)
    q3_index = int(sample_size * 0.75)

    # Ensure indices are within bounds
    q1_index = max(0, min(q1_index, sample_size - 1))
    q3_index = max(0, min(q3_index, sample_size - 1))

    q1 = sorted_data[q1_index]
    q3 = sorted_data[q3_index]
    iqr = q3 - q1

    if iqr > 0 and sample_size > 1:
        fd_bin_width = 2 * iqr / (sample_size ** (1.0/3.0))
        fd_bins = int((max_val - min_val) / fd_bin_width + 0.5) if fd_bin_width > 0 else sturges_bins
    else:
        fd_bins = sturges_bins

    # Method 4: Square Root Rule - simple and often effective
    sqrt_bins = int(math.sqrt(sample_size) + 0.5)

    # Apply constraints
    min_bins = 5
    max_bins = 50

    sturges_bins = max(min_bins, min(max_bins, sturges_bins))
    scott_bins = max(min_bins, min(max_bins, scott_bins))
    fd_bins = max(min_bins, min(max_bins, fd_bins))
    sqrt_bins = max(min_bins, min(max_bins, sqrt_bins))

    # Choose the best method based on sample size
    if sample_size < 20:
        # Small samples: use conservative approach
        optimal_bins = min(sturges_bins, sqrt_bins)
    elif sample_size < 100:
        # Medium samples: prefer Sturges or Square Root
        optimal_bins = int((sturges_bins + sqrt_bins) / 2)
    else:
        # Large samples: use more sophisticated methods
        if abs(fd_bins - scott_bins) < 5:
            optimal_bins = int((fd_bins + scott_bins) / 2)
        else:
            optimal_bins = fd_bins  # More robust to outliers

    # Final constraint check
    optimal_bins = max(min_bins, min(max_bins, optimal_bins))

    # Ensure we don't have more bins than unique values
    unique_values = len(set(data_array))
    if optimal_bins > unique_values:
        optimal_bins = max(min_bins, unique_values)

    return optimal_bins

def create_imagej_histogram_plot(pore_areas, optimal_bins, min_area, max_area, bin_frequencies, bin_edges):
    """Create a histogram plot using ImageJ's built-in Plot class with exact bin control."""
    try:
        from ij.gui import Plot
        import tempfile

        if not bin_frequencies or not any(bin_frequencies):
            return None

        # Add statistics to title
        mean_area = sum(pore_areas) / len(pore_areas)
        title = "Pore Size Distribution (Total: {}, Mean: {:.2f} um^2)".format(
            len(pore_areas), mean_area)

        # Create the plot
        plot = Plot(title, "Pore Area (um^2)", "Frequency")
        plot.setSize(800, 600)

        # Set plot limits with some padding
        x_range = max_area - min_area if max_area > min_area else 1
        y_max = max(bin_frequencies) if bin_frequencies else 1
        plot.setLimits(min_area - x_range * 0.05, max_area + x_range * 0.05,
                      0, y_max * 1.1)

        # Create histogram using exact bin boundaries
        plot.setColor("blue")
        plot.setLineWidth(2)

        # For each bin, create a rectangular bar
        for i in range(len(bin_edges) - 1):
            bin_start = bin_edges[i]
            bin_end = bin_edges[i + 1]
            frequency = bin_frequencies[i]

            if frequency > 0:
                # Create the four corners of the rectangle
                x_coords = [bin_start, bin_start, bin_end, bin_end]
                y_coords = [0, frequency, frequency, 0]

                # Draw the bar outline
                plot.addPoints(x_coords, y_coords, Plot.LINE)

                # Add a filled appearance by drawing multiple horizontal lines
                fill_density = max(1, int(frequency / 2))  # Number of fill lines
                for j in range(1, fill_density):
                    y_fill = frequency * j / fill_density
                    plot.addPoints([bin_start, bin_end], [y_fill, y_fill], Plot.LINE)

        # Show the plot (this creates the plot window)
        plot.show()

        # Get the plot window and save as PNG
        plot_window = WindowManager.getWindow(title)
        if plot_window:
            # Save to temporary file
            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, "pore_histogram_imagej.png")

            # Save the plot as PNG
            IJ.selectWindow(title)
            IJ.run("PNG...", "path={}".format(temp_path))

            # Close the plot window
            plot_window.close()

            return temp_path
        else:
            IJ.log("Warning: Could not create ImageJ plot window")
            return None

    except Exception as e:
        IJ.log("Error creating ImageJ histogram plot: {}".format(str(e)))
        return None


def embed_image_in_excel(workbook, sheet, image_path, col_index, row_index):
    """Embed an image into an Excel worksheet."""
    try:
        from java.io import FileInputStream
        from org.apache.poi.util import IOUtils

        # Read the image file
        image_stream = FileInputStream(image_path)
        image_bytes = IOUtils.toByteArray(image_stream)
        image_stream.close()

        # Add picture to workbook
        picture_index = workbook.addPicture(image_bytes, workbook.PICTURE_TYPE_PNG)

        # Create drawing and anchor
        drawing = sheet.createDrawingPatriarch()
        anchor = workbook.getCreationHelper().createClientAnchor()

        # Set anchor position (column, row)
        anchor.setCol1(col_index)
        anchor.setRow1(row_index)
        anchor.setCol2(col_index + 8)  # Span 8 columns
        anchor.setRow2(row_index + 20)  # Span 20 rows

        # Create picture
        picture = drawing.createPicture(anchor, picture_index)

        # Clean up temporary file
        try:
            os.remove(image_path)
        except:
            pass

        return True

    except Exception as e:
        IJ.log("Error embedding image in Excel: {}".format(str(e)))
        return False


def create_weka_instances_and_classify_dual(particle_features, feature_names, porosity_classifier, shape_classifier):
    """Create Weka instances and perform dual classification."""
    if not particle_features:
        return []

    # Order features for each classifier
    porosity_ordered_features = []
    shape_ordered_features = []

    for features in particle_features:
        porosity_row = [features[feature_names.index(name)] for name in POROSITY_REQUIRED_FEATURES if name in feature_names]
        shape_row = [features[feature_names.index(name)] for name in SHAPE_REQUIRED_FEATURES if name in feature_names]

        if len(porosity_row) == len(POROSITY_REQUIRED_FEATURES) and len(shape_row) == len(SHAPE_REQUIRED_FEATURES):
            porosity_ordered_features.append(porosity_row)
            shape_ordered_features.append(shape_row)

    if not porosity_ordered_features:
        return []

    # Create Weka instances for porosity
    porosity_attributes = ArrayList()
    for name in POROSITY_REQUIRED_FEATURES:
        porosity_attributes.add(WekaAttribute(name.replace(" ", "_")))
    porosity_labels_list = ArrayList()
    for label in POROSITY_LABELS:
        porosity_labels_list.add(label)
    porosity_attributes.add(WekaAttribute("Porosity", porosity_labels_list))
    porosity_instances = WekaInstances("PorosityData", porosity_attributes, len(porosity_ordered_features))
    porosity_instances.setClassIndex(porosity_instances.numAttributes() - 1)

    # Create Weka instances for shape
    shape_attributes = ArrayList()
    for name in SHAPE_REQUIRED_FEATURES:
        shape_attributes.add(WekaAttribute(name.replace(" ", "_")))
    shape_labels_list = ArrayList()
    for label in SHAPE_LABELS:
        shape_labels_list.add(label)
    shape_attributes.add(WekaAttribute("Shape", shape_labels_list))
    shape_instances = WekaInstances("ShapeData", shape_attributes, len(shape_ordered_features))
    shape_instances.setClassIndex(shape_instances.numAttributes() - 1)

    # Perform dual classification
    dual_predictions = []
    for porosity_feature_vector, shape_feature_vector in zip(porosity_ordered_features, shape_ordered_features):
        # Porosity classification
        porosity_instance = WekaDenseInstance(len(porosity_feature_vector) + 1)
        porosity_instance.setDataset(porosity_instances)
        for j, value in enumerate(porosity_feature_vector):
            porosity_instance.setValue(j, float(value))
        porosity_instance.setClassMissing()

        # Shape classification
        shape_instance = WekaDenseInstance(len(shape_feature_vector) + 1)
        shape_instance.setDataset(shape_instances)
        for j, value in enumerate(shape_feature_vector):
            shape_instance.setValue(j, float(value))
        shape_instance.setClassMissing()

        try:
            porosity_class = POROSITY_LABELS[int(porosity_classifier.classifyInstance(porosity_instance))]
        except:
            porosity_class = "unknown"

        try:
            shape_class = SHAPE_LABELS[int(shape_classifier.classifyInstance(shape_instance))]
        except:
            shape_class = "unknown"

        dual_predictions.append((porosity_class, shape_class))

    return dual_predictions


def apply_visualization_and_save(imp, dual_predictions, image_path):
    """Consolidated function for coloring, numbering, legend, and saving."""
    roim = RoiManager.getInstance()
    if not roim or roim.getCount() == 0:
        return

    # Color particles
    for i in range(min(roim.getCount(), len(dual_predictions))):
        roi = roim.getRoi(i)
        porosity_class, shape_class = dual_predictions[i]
        color = DUAL_COLORS.get((porosity_class, shape_class), Color.GRAY)
        roi.setFillColor(color)
        roi.setStrokeColor(color)

    # Show ROIs - we control numbering manually through overlay
    roim.runCommand("Show All without labels")

    # Create overlay for numbers and legend
    overlay = Overlay()

    # Add numbers if enabled
    if SHOW_PARTICLE_NUMBERS:
        for i in range(roim.getCount()):
            roi = roim.getRoi(i)
            if roi:
                bounds = roi.getBounds()
                center_x = bounds.x + bounds.width / 2
                center_y = bounds.y + bounds.height / 2
                text_roi = TextRoi(center_x - 8, center_y - 8, str(i + 1))
                text_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 16))
                text_roi.setFillColor(Color.WHITE)
                text_roi.setStrokeColor(Color.BLACK)
                text_roi.setStrokeWidth(2)
                overlay.add(text_roi)

    # Add legend if enabled
    if CREATE_LEGEND:
        legend_x, legend_y = 10, 10
        legend_width, legend_height = 320, 200

        # Background
        bg_roi = Roi(legend_x, legend_y, legend_width, legend_height)
        bg_roi.setFillColor(Color(255, 255, 255, 180))
        bg_roi.setStrokeColor(Color.BLACK)
        overlay.add(bg_roi)

        # Title
        title_roi = TextRoi(legend_x + 10, legend_y + 10, "Classification Legend")
        title_roi.setCurrentFont(Font("SansSerif", Font.BOLD, 24))
        title_roi.setFillColor(Color.BLACK)
        overlay.add(title_roi)

        # Color entries
        y_offset, entry_height = 50, 36
        for i, (porosity_class, shape_class) in enumerate([
            ("NonPorous", "Round"), ("NonPorous", "Imperfect"),
            ("Porous", "Round"), ("Porous", "Imperfect")
        ]):
            color = DUAL_COLORS.get((porosity_class, shape_class), Color.GRAY)

            # Color square
            color_roi = Roi(legend_x + 10, legend_y + y_offset + i * entry_height, 24, 24)
            color_roi.setFillColor(color)
            color_roi.setStrokeColor(Color.BLACK)
            color_roi.setStrokeWidth(2)
            overlay.add(color_roi)

            # Text label
            label_text = porosity_class + " + " + shape_class
            text_roi = TextRoi(legend_x + 44, legend_y + y_offset + i * entry_height, label_text + " ")
            text_roi.setCurrentFont(Font("SansSerif", Font.PLAIN, 18))
            text_roi.setFillColor(Color.BLACK)
            overlay.add(text_roi)

    # Set overlay and update
    imp.setOverlay(overlay)
    imp.updateAndDraw()

    # Create PNG with overlays based on configuration
    if SHOW_IMAGES_AFTER_PROCESSING or SAVE_PROCESSED_IMAGES:
        try:
            output_dir = PROCESSED_IMAGES_OUTPUT_DIR if PROCESSED_IMAGES_OUTPUT_DIR and os.path.exists(PROCESSED_IMAGES_OUTPUT_DIR) else os.path.dirname(image_path)
            name_without_ext = os.path.splitext(os.path.basename(image_path))[0]

            if SHOW_PARTICLE_NUMBERS:
                output_filename = "classified_numbered_" + name_without_ext + ".png"
            else:
                output_filename = "classified_colored_" + name_without_ext + ".png"

            output_path = os.path.join(output_dir, output_filename)

            # Flatten to create PNG with overlays
            IJ.run(imp, "Flatten", "")
            flattened_imp = WindowManager.getCurrentImage()

            if flattened_imp:
                # Set proper title for the flattened image (always, regardless of save/display settings)
                if SHOW_PARTICLE_NUMBERS:
                    display_title = "numbered_" + name_without_ext
                else:
                    display_title = "coloured_" + name_without_ext
                flattened_imp.setTitle(display_title)

                # Save to disk if SAVE_PROCESSED_IMAGES is True
                if SAVE_PROCESSED_IMAGES:
                    IJ.saveAs(flattened_imp, "PNG", output_path)

                # Close flattened image if not showing after processing
                if not SHOW_IMAGES_AFTER_PROCESSING:
                    flattened_imp.changes = False
                    flattened_imp.close()
        except Exception as e:
            IJ.log("Error creating/saving processed image: " + str(e))


def process_single_image(image_path, porosity_classifier, shape_classifier):
    """Process a single image with dual classification and return both stats and image reference."""
    # Open original image
    original_imp = IJ.openImage(image_path)
    if not original_imp:
        return None, None

    # Create a duplicate for macro processing (this will be modified by macros)
    analysis_imp = original_imp.duplicate()
    analysis_imp.setTitle("Analysis_" + original_imp.getTitle())

    # Show the analysis image as current for macro processing
    analysis_imp.show()

    # Extract features and classify using the analysis duplicate
    particle_features, feature_names, _ = run_macro_and_extract_features()
    if not particle_features:
        analysis_imp.changes = False
        analysis_imp.close()
        original_imp.changes = False
        original_imp.close()
        return None, None

    dual_predictions = create_weka_instances_and_classify_dual(particle_features, feature_names, porosity_classifier, shape_classifier)

    # Check if we have valid predictions
    if not dual_predictions:
        analysis_imp.changes = False
        analysis_imp.close()
        original_imp.changes = False
        original_imp.close()
        return None, None

    # Store ROIs from analysis for transfer to original image
    roim = RoiManager.getInstance()
    stored_rois = []
    if roim and roim.getCount() > 0:
        # Store all ROIs from the analysis
        for i in range(roim.getCount()):
            roi = roim.getRoi(i)
            if roi:
                stored_rois.append(roi.clone())

    # Now apply visualization to the ORIGINAL unprocessed image
    if SHOW_IMAGES_AFTER_PROCESSING or CREATE_LEGEND or SAVE_PROCESSED_IMAGES:
        # Switch to original image for visualization
        if SHOW_IMAGES_AFTER_PROCESSING:
            original_imp.show()
        else:
            WindowManager.setTempCurrentImage(original_imp)

        # Clear ROI manager and transfer ROIs from analysis to original image
        roim = RoiManager.getInstance()
        if roim: roim.reset()

        # Add stored ROIs to the original image
        for roi in stored_rois:
            roim.addRoi(roi)

        try:
            # Apply visualization to the original pristine image (NO macro re-run!)
            apply_visualization_and_save(original_imp, dual_predictions, image_path)
        except Exception as e:
            IJ.log("Warning: Visualization failed: " + str(e))

    # Close the analysis duplicate (no longer needed)
    analysis_imp.changes = False
    analysis_imp.close()

    # Calculate statistics
    dual_stats = {}
    for porosity_class in POROSITY_LABELS:
        for shape_class in SHAPE_LABELS:
            combo = (porosity_class, shape_class)
            dual_stats[combo] = dual_predictions.count(combo)

    porosity_stats = {porosity_class: sum(1 for p, _ in dual_predictions if p == porosity_class) for porosity_class in POROSITY_LABELS}
    shape_stats = {shape_class: sum(1 for _, s in dual_predictions if s == shape_class) for shape_class in SHAPE_LABELS}

    # Return both statistics and the ORIGINAL image with overlays (for pore extraction)
    return {'dual': dual_stats, 'porosity': porosity_stats, 'shape': shape_stats}, original_imp


# === MAIN PROCESSING ===
def main():
    """Main processing function."""
    start_time = time.time()

    IJ.log("\\Clear")
    IJ.log("=" * 60)
    IJ.log("FIJI/ImageJ Compact Batch Particle Classification v2.8")
    IJ.log("=" * 60)

    try:
        # Setup and validation
        setup_weka_and_validate()

        # Load models
        porosity_classifier = load_weka_model(POROSITY_MODEL_PATH)
        shape_classifier = load_weka_model(SHAPE_MODEL_PATH)
        if not porosity_classifier or not shape_classifier:
            raise Exception("Failed to load classification models")

        IJ.log("Both models loaded successfully")

        # Get image files
        file_types = FILE_TYPES.split(';') if FILE_TYPES else ['tif']
        image_files = []
        filenames = []
        for filename in os.listdir(IMPORT_DIR):
            if any(filename.lower().endswith('.' + ft.strip().lower()) for ft in file_types):
                image_files.append(os.path.join(IMPORT_DIR, filename))
                filenames.append(filename)

        if not image_files:
            raise Exception("No image files found in directory")

        IJ.log("Found {} image files in directory".format(len(image_files)))

        # Load existing CSV data
        csv_path = os.path.join(os.path.dirname(IMPORT_DIR), "particle_dual_predictions.csv")
        existing_csv_data, processed_images_from_csv, existing_stats = load_existing_csv_data(csv_path, filenames)

        # Load existing pore data from Excel file, filtered by current images in directory
        excel_path = csv_path.replace('.csv', '.xlsx')
        if os.path.exists(excel_path):
            try:
                # Get list of current image names (basenames only) and convert to RichString for comparison
                from org.apache.poi.xssf.usermodel import XSSFRichTextString
                current_image_names = set()
                for filename in filenames:
                    rich_filename = XSSFRichTextString(filename)
                    current_image_names.add(rich_filename)
                load_existing_pore_data_from_excel(excel_path, current_image_names)
            except Exception as e:
                IJ.log("Warning: Could not load existing pore data from Excel: {}".format(str(e)))

        # Count how many images actually need processing
        images_to_process = []
        images_already_processed = []
        total_images_found = len(image_files)

        for image_path in image_files:
            image_name = os.path.basename(image_path)

            if is_image_already_processed(image_path, processed_images_from_csv):
                normalized_name = normalize_filename(image_name)
                images_already_processed.append(normalized_name)
            else:
                images_to_process.append(image_path)

        if images_already_processed:
            IJ.log("Images already processed: {} (will be skipped)".format(len(images_already_processed)))
            # Safely log skipped image names with Unicode handling
            try:
                # Use normalized names and limit to first 5 for readability
                safe_names = [normalize_filename(name) for name in images_already_processed[:5]]
                skipped_list = ", ".join(safe_names)
                if len(images_already_processed) > 5:
                    skipped_list += "..."
                IJ.log("  -> Skipped images: {}".format(skipped_list))
            except:
                IJ.log("  -> Skipped images: [Unicode filenames - {} total]".format(len(images_already_processed)))
        IJ.log("Images to process: {}".format(len(images_to_process)))

        # Show incremental processing status
        if len(processed_images_from_csv) > 0:
            IJ.log("INCREMENTAL PROCESSING MODE: Adding {} new images to existing {} processed images".format(
                len(images_to_process), len(processed_images_from_csv)))

        # Update image_files to only include those that need processing
        image_files = images_to_process

        # Initialize statistics (start with existing stats from CSV)
        all_predictions = []
        total_dual_stats = existing_stats['dual'].copy()
        total_porosity_stats = existing_stats['porosity'].copy()
        total_shape_stats = existing_stats['shape'].copy()
        processed_count = 0
        new_images_processed = 0

        # Process images (already filtered to only include unprocessed ones)
        for image_path in image_files:
            current_image = None
            try:
                image_name = os.path.basename(image_path)
                normalized_name = normalize_filename(image_name)
                IJ.log("PROCESSING: " + normalized_name)
                prediction_stats, current_image = process_single_image(image_path, porosity_classifier, shape_classifier)

                if prediction_stats:
                    normalized_image_name = normalize_filename(image_name)
                    all_predictions.append((normalized_image_name, prediction_stats))

                    # Update totals
                    for combo, count in prediction_stats['dual'].items():
                        total_dual_stats[combo] += count
                    for label, count in prediction_stats['porosity'].items():
                        total_porosity_stats[label] += count
                    for label, count in prediction_stats['shape'].items():
                        total_shape_stats[label] += count

                    processed_count += 1
                    new_images_processed += 1
                    IJ.log("  -> Processed successfully")

                    # Extract pore sizes while image is still open
                    if current_image:
                        pore_count = extract_pore_sizes(image_name, current_image)
                        if pore_count > 0:
                            IJ.log("  -> Found {} pores".format(pore_count))
                        else:
                            IJ.log("  -> No pores found")
                else:
                    IJ.log("  -> Processing failed")

            except Exception as e:
                safe_name = normalize_filename(os.path.basename(image_path))
                IJ.log("ERROR processing {}: {}".format(safe_name, str(e)))
            finally:
                # Always close the image at the end of processing
                if current_image:
                    current_image.changes = False
                    current_image.close()

        # Export CSV with incremental updates
        if new_images_processed > 0 or not os.path.exists(csv_path):
            try:
                csv_content = []

                # Collect existing data first (if any)
                if existing_csv_data:
                    for line in existing_csv_data:
                        csv_content.append(line + '\n')
                else:
                    # Create header for new CSV
                    dual_headers = [p + "_" + s for p in POROSITY_LABELS for s in SHAPE_LABELS]
                    porosity_headers = ["porosity_" + label for label in POROSITY_LABELS]
                    shape_headers = ["shape_" + label for label in SHAPE_LABELS]
                    header = 'Image,' + ','.join(dual_headers) + ',' + ','.join(porosity_headers) + ',' + ','.join(shape_headers)
                    csv_content.append(header + '\n')

                # Collect new data rows
                for img_name, img_stats in all_predictions:
                    row_data = [img_name]
                    for p in POROSITY_LABELS:
                        for s in SHAPE_LABELS:
                            row_data.append(str(img_stats['dual'].get((p, s), 0)))
                    for label in POROSITY_LABELS:
                        row_data.append(str(img_stats['porosity'].get(label, 0)))
                    for label in SHAPE_LABELS:
                        row_data.append(str(img_stats['shape'].get(label, 0)))
                    csv_content.append(','.join(row_data) + '\n')

                # Calculate and collect totals row
                total_row = ['TOTAL']
                for p in POROSITY_LABELS:
                    for s in SHAPE_LABELS:
                        total_row.append(str(total_dual_stats.get((p, s), 0)))
                for label in POROSITY_LABELS:
                    total_row.append(str(total_porosity_stats.get(label, 0)))
                for label in SHAPE_LABELS:
                    total_row.append(str(total_shape_stats.get(label, 0)))
                csv_content.append(','.join(total_row) + '\n')

                # Document used calibration
                csv_content.append('# Calibration: {} pixels = {} microns\n'.format(pixels, microns))

                # Write all content as binary with latin-1 encoding
                full_content = ''.join(csv_content)
                with open(csv_path, 'wb') as f:
                    f.write(full_content.encode('latin-1'))

                # Try to create formatted Excel file
                excel_created = create_formatted_excel_file(csv_path, [line.strip() for line in csv_content])

                # Verify final CSV integrity
                total_csv_images = len(processed_images_from_csv) + new_images_processed
                expected_total_images = len(processed_images_from_csv) + len(images_to_process)

                if excel_created:
                    IJ.log("CSV and Excel files updated successfully with {} new images".format(new_images_processed))
                else:
                    IJ.log("CSV updated successfully with {} new images".format(new_images_processed))

                IJ.log("CSV integrity check: {} total images in final CSV (expected: {})".format(
                    total_csv_images, expected_total_images))

            except Exception as e:
                IJ.log("ERROR: Failed to update CSV: " + str(e))

        # Final statistics
        end_time = time.time()
        elapsed = end_time - start_time
        total_particles = sum(total_porosity_stats.values())
        existing_particles = sum(existing_stats['porosity'].values())
        new_particles = total_particles - existing_particles

        IJ.log("\n" + "=" * 60)
        IJ.log("DUAL CLASSIFICATION PROCESSING COMPLETE")
        IJ.log("=" * 60)
        IJ.log("Images found in directory: {}".format(total_images_found))
        IJ.log("Images newly processed: {}".format(new_images_processed))
        IJ.log("Images skipped (already processed): {}".format(len(images_already_processed)))
        IJ.log("Total images in CSV: {}".format(len(processed_images_from_csv) + new_images_processed))
        IJ.log("Total particles: {} ({} existing + {} new)".format(total_particles, existing_particles, new_particles))
        IJ.log("Processing time: {}s".format(round(elapsed, 1)))
        if processed_count > 0:
            IJ.log("Average time per image: {}s".format(round(elapsed / processed_count, 1)))

        IJ.log("\nPorosity Classification Results:")
        for label, count in total_porosity_stats.items():
            percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
            IJ.log("  {}: {} ({}%)".format(label, count, round(percentage, 1)))

        IJ.log("\nShape Classification Results:")
        for label, count in total_shape_stats.items():
            percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
            IJ.log("  {}: {} ({}%)".format(label, count, round(percentage, 1)))

        IJ.log("\nDual Classification Combinations:")
        for porosity_class in POROSITY_LABELS:
            for shape_class in SHAPE_LABELS:
                combo = (porosity_class, shape_class)
                count = total_dual_stats.get(combo, 0)
                percentage = (count / float(total_particles) * 100) if total_particles > 0 else 0
                IJ.log("  {} + {}: {} ({}%)".format(porosity_class, shape_class, count, round(percentage, 1)))

        # Pore analysis statistics
        if all_pore_data:
            pore_areas = [pore["pore_area"] for pore in all_pore_data]
            total_pores = len(all_pore_data)
            min_pore = min(pore_areas)
            max_pore = max(pore_areas)
            mean_pore = sum(pore_areas) / total_pores
            median_pore = sorted(pore_areas)[total_pores // 2]

            IJ.log("\nPore Analysis Results:")
            IJ.log("Total pores found: {}".format(total_pores))
            IJ.log("Pore area range: {:.3f} - {:.3f} um^2".format(min_pore, max_pore))
            IJ.log("Mean pore area: {:.3f} um^2".format(mean_pore))
            IJ.log("Median pore area: {:.3f} um^2".format(median_pore))

        excel_path = csv_path.replace('.csv', '.xlsx')
        if os.path.exists(excel_path):
            IJ.log("Results exported to: {} (formatted Excel) and {} (CSV)".format(excel_path, csv_path))
        else:
            IJ.log("Results exported to: {} (CSV)".format(csv_path))

        IJ.log("=" * 60)

        # Cleanup
        try:
            rt = ResultsTable.getResultsTable()
            if rt:
                rt.show("Results")
                IJ.selectWindow("Results")
                IJ.run("Close")
            roim = RoiManager.getInstance()
            if roim:
                IJ.selectWindow("ROI Manager")
                IJ.run("Close")
        except:
            pass

        return True

    except Exception as e:
        IJ.log("ERROR: " + str(e))
        return False


# === SCRIPT EXECUTION ===
if __name__ == '__main__':
    try:
        success = main()
        if success:
            IJ.log("Script completed successfully!")
        else:
            IJ.log("Script failed. Check the log for details.")
    except Exception as e:
        IJ.log("Unexpected error: " + str(e))
